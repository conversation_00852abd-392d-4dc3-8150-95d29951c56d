* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr 1fr;
}

section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

section:first-child,
section:nth-child(2) {
    grid-column: 1 / -1;
}

section h2 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Form Styles */
form {
    display: grid;
    gap: 1rem;
}

form div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

label {
    font-weight: 600;
    color: #555;
}

input[type="text"],
input[type="number"] {
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 0.5rem;
}

button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

#bookFormSubmit,
#searchSubmit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#bookFormSubmit:hover,
#searchSubmit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Book Item Styles */
div[data-testid="bookItem"] {
    background: #f8f9ff;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    border-left: 5px solid #667eea;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

div[data-testid="bookItem"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

h3[data-testid="bookItemTitle"] {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

p[data-testid="bookItemAuthor"],
p[data-testid="bookItemYear"] {
    color: #666;
    margin-bottom: 0.3rem;
}

div[data-testid="bookItem"] > div {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

button[data-testid="bookItemIsCompleteButton"] {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemDeleteButton"] {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemEditButton"] {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemIsCompleteButton"]:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
    transform: translateY(-1px);
}

button[data-testid="bookItemDeleteButton"]:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    transform: translateY(-1px);
}

button[data-testid="bookItemEditButton"]:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
    transform: translateY(-1px);
}

/* Search Form */
#searchBook {
    grid-template-columns: 1fr auto;
    align-items: end;
    gap: 1rem;
}

#searchBook div {
    flex-direction: column;
}

/* Empty State */
div[data-testid="incompleteBookList"]:empty::after,
div[data-testid="completeBookList"]:empty::after {
    content: "Tidak ada buku dalam kategori ini.";
    display: block;
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 5px;
    border: 2px dashed #ddd;
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    section {
        padding: 1.5rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    #searchBook {
        grid-template-columns: 1fr;
    }
    
    div[data-testid="bookItem"] > div {
        flex-direction: column;
    }
    
    div[data-testid="bookItem"] > div button {
        width: 100%;
        text-align: center;
    }
}

/* Form checkbox styling */
form div:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

/* Animation for book additions */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

div[data-testid="bookItem"] {
    animation: slideIn 0.3s ease-out;
}