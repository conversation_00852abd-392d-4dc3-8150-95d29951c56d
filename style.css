/**
 * APLIKASI RAK BUKU DIGITAL - STYLESHEET
 * ======================================
 * File CSS untuk styling aplikasi bookshelf dengan desain modern
 * Menggunakan gradient, animasi, dan responsive design
 */

/* RESET CSS - Menghilangkan default styling browser */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box; /* Membuat padding dan border termasuk dalam ukuran elemen */
}

/* STYLING DASAR BODY */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;           /* Jarak antar baris untuk keterbacaan */
    color: #333;                /* Warna teks utama */
    background-color: #f4f4f4;  /* Warna background abu-abu terang */
}

/* ===========================
   STYLING HEADER
   =========================== */

/* Container utama header dengan gradient background */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* Gradient ungu-biru */
    color: white;
    text-align: center;
    padding: 2rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* Bayangan halus */
}

/* Judul aplikasi */
header h1 {
    font-size: 2.5rem;
    font-weight: 300;        /* Font tipis untuk tampilan modern */
    margin: 0 0 1rem 0;
}

/* Container untuk tombol-tombol aksi di header */
.header-actions {
    display: flex;
    justify-content: center; /* Pusatkan tombol */
    gap: 1rem;              /* Jarak antar tombol */
    flex-wrap: wrap;        /* Bungkus ke baris baru jika perlu */
    margin-top: 1rem;
}

/* Styling tombol-tombol di header */
.header-actions button {
    background: rgba(255, 255, 255, 0.2);      /* Background transparan */
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3); /* Border transparan */
    padding: 0.5rem 1rem;
    border-radius: 25px;                        /* Sudut membulat */
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;                  /* Animasi smooth */
    backdrop-filter: blur(10px);                /* Efek blur background */
}

/* Efek hover untuk tombol header */
.header-actions button:hover {
    background: rgba(255, 255, 255, 0.3);      /* Background lebih terang saat hover */
    border-color: rgba(255, 255, 255, 0.5);    /* Border lebih terang */
    transform: translateY(-2px);                /* Efek naik sedikit */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Bayangan lebih dalam */
}

/* Efek saat tombol ditekan */
.header-actions button:active {
    transform: translateY(0); /* Kembali ke posisi normal */
}

/* ===========================
   LAYOUT UTAMA
   =========================== */

/* Container utama konten */
main {
    max-width: 1200px;           /* Lebar maksimal */
    margin: 2rem auto;           /* Pusatkan dengan margin atas-bawah */
    padding: 0 1rem;             /* Padding kiri-kanan */
    display: grid;               /* Gunakan CSS Grid */
    gap: 2rem;                   /* Jarak antar section */
    grid-template-columns: 1fr 1fr; /* 2 kolom sama lebar */
}

/* Styling untuk setiap section */
section {
    background: white;                          /* Background putih */
    padding: 2rem;                             /* Padding dalam */
    border-radius: 10px;                       /* Sudut membulat */
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);    /* Bayangan halus */
}

/* Section pertama dan kedua mengambil lebar penuh */
section:first-child,
section:nth-child(2) {
    grid-column: 1 / -1; /* Dari kolom 1 sampai akhir */
}

/* Styling heading section */
section h2 {
    color: #667eea;                    /* Warna biru sesuai tema */
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 3px solid #667eea;  /* Garis bawah sebagai aksen */
    padding-bottom: 0.5rem;
    position: relative;
}

/* ===========================
   STYLING FORM
   =========================== */

/* Container form menggunakan grid layout */
form {
    display: grid;
    gap: 1rem; /* Jarak antar field form */
}

/* Container untuk setiap field form */
form div {
    display: flex;
    flex-direction: column; /* Label di atas, input di bawah */
    gap: 0.5rem;           /* Jarak antara label dan input */
}

/* Styling label form */
label {
    font-weight: 600; /* Font tebal */
    color: #555;      /* Warna abu-abu gelap */
}

/* Styling input text dan number */
input[type="text"],
input[type="number"] {
    padding: 0.75rem;                    /* Padding dalam input */
    border: 2px solid #ddd;              /* Border abu-abu */
    border-radius: 5px;                  /* Sudut membulat */
    font-size: 1rem;
    transition: border-color 0.3s ease;  /* Animasi perubahan border */
}

/* Efek focus pada input */
input[type="text"]:focus,
input[type="number"]:focus {
    outline: none;                                    /* Hilangkan outline default */
    border-color: #667eea;                           /* Border biru saat focus */
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); /* Bayangan biru halus */
}

/* Styling checkbox */
input[type="checkbox"] {
    transform: scale(1.2); /* Perbesar checkbox */
    margin-right: 0.5rem;  /* Jarak ke kanan */
}

/* ===========================
   STYLING TOMBOL
   =========================== */

/* Styling dasar untuk semua tombol */
button {
    padding: 0.75rem 1.5rem;    /* Padding dalam tombol */
    border: none;                /* Hilangkan border default */
    border-radius: 5px;          /* Sudut membulat */
    cursor: pointer;             /* Cursor pointer saat hover */
    font-size: 1rem;
    font-weight: 600;            /* Font tebal */
    transition: all 0.3s ease;   /* Animasi smooth untuk semua perubahan */
}

/* Tombol submit form dan pencarian - warna biru gradient */
#bookFormSubmit,
#searchSubmit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Efek hover untuk tombol utama */
#bookFormSubmit:hover,
#searchSubmit:hover {
    transform: translateY(-2px);                        /* Naik sedikit */
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);   /* Bayangan biru */
}

/* Tombol bersihkan pencarian - warna abu-abu gradient */
#clearSearch {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    margin-left: 0.5rem; /* Jarak dari tombol cari */
}

/* Efek hover untuk tombol bersihkan */
#clearSearch:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
}

/* ===========================
   STYLING ITEM BUKU
   =========================== */

/* Container untuk setiap item buku */
div[data-testid="bookItem"] {
    background: #f8f9ff;                                      /* Background biru sangat muda */
    padding: 1.5rem;                                          /* Padding dalam */
    margin-bottom: 1rem;                                      /* Jarak antar item */
    border-radius: 8px;                                       /* Sudut membulat */
    border-left: 5px solid #667eea;                          /* Accent border kiri */
    transition: transform 0.2s ease, box-shadow 0.2s ease;   /* Animasi hover */
}

/* Efek hover pada item buku */
div[data-testid="bookItem"]:hover {
    transform: translateY(-2px);                /* Naik sedikit */
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);    /* Bayangan lebih dalam */
}

/* Styling judul buku */
h3[data-testid="bookItemTitle"] {
    color: #333;                /* Warna gelap */
    margin-bottom: 0.5rem;
    font-size: 1.3rem;          /* Ukuran font lebih besar */
}

/* Styling penulis dan tahun buku */
p[data-testid="bookItemAuthor"],
p[data-testid="bookItemYear"] {
    color: #666;                /* Warna abu-abu */
    margin-bottom: 0.3rem;
}

/* Container untuk tombol-tombol aksi buku */
div[data-testid="bookItem"] > div {
    display: flex;              /* Layout horizontal */
    gap: 0.5rem;               /* Jarak antar tombol */
    margin-top: 1rem;          /* Jarak dari konten di atas */
    flex-wrap: wrap;           /* Bungkus ke baris baru jika perlu */
}

/* Tombol ubah status baca - warna hijau */
button[data-testid="bookItemIsCompleteButton"] {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* Tombol hapus buku - warna merah */
button[data-testid="bookItemDeleteButton"] {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* Tombol edit buku - warna oranye */
button[data-testid="bookItemEditButton"] {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* Efek hover untuk tombol-tombol aksi buku */
button[data-testid="bookItemIsCompleteButton"]:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%); /* Balik gradient */
    transform: translateY(-1px);                                   /* Naik sedikit */
}

button[data-testid="bookItemDeleteButton"]:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    transform: translateY(-1px);
}

button[data-testid="bookItemEditButton"]:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
    transform: translateY(-1px);
}

/* ===========================
   STYLING FORM PENCARIAN
   =========================== */

/* Layout form pencarian menggunakan grid */
#searchBook {
    grid-template-columns: 1fr auto; /* Input mengambil sisa ruang, tombol sesuai konten */
    align-items: end;                /* Sejajarkan ke bawah */
    gap: 1rem;                      /* Jarak antar elemen */
}

/* Container field pencarian */
#searchBook div {
    flex-direction: column; /* Label di atas, input di bawah */
}

/* Container tombol pencarian */
#searchBook div:last-child {
    flex-direction: row;    /* Tombol berjajar horizontal */
    gap: 0.5rem;           /* Jarak antar tombol */
}

/* ===========================
   EMPTY STATE (KOSONG)
   =========================== */

/* Pesan ketika tidak ada buku dalam kategori */
div[data-testid="incompleteBookList"]:empty::after,
div[data-testid="completeBookList"]:empty::after {
    content: "📚 Tidak ada buku dalam kategori ini.";
    display: block;
    text-align: center;
    color: #999;                                                    /* Warna abu-abu */
    font-style: italic;                                             /* Font miring */
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); /* Background gradient abu */
    border-radius: 10px;
    border: 2px dashed #ddd;                                        /* Border putus-putus */
    font-size: 1.1rem;
}

/* ===========================
   STYLING KHUSUS CHECKBOX
   =========================== */

/* Container checkbox - layout horizontal */
form div:has(input[type="checkbox"]) {
    flex-direction: row;    /* Checkbox dan label berjajar horizontal */
    align-items: center;    /* Sejajarkan vertikal */
    gap: 0.5rem;           /* Jarak antara checkbox dan label */
}

/* ===========================
   STYLING MODAL EDIT
   =========================== */

/* Overlay modal yang menutupi seluruh layar */
.modal {
    display: none;                          /* Tersembunyi secara default */
    position: fixed;                        /* Posisi tetap relatif ke viewport */
    z-index: 1000;                         /* Layer paling atas */
    left: 0;
    top: 0;
    width: 100%;                           /* Lebar penuh */
    height: 100%;                          /* Tinggi penuh */
    overflow: auto;                        /* Scroll jika konten terlalu panjang */
    background-color: rgba(0, 0, 0, 0.5);  /* Background hitam transparan */
    backdrop-filter: blur(5px);            /* Efek blur pada background */
    animation: fadeIn 0.3s ease-out;       /* Animasi fade in */
}

/* Animasi fade in untuk modal */
@keyframes fadeIn {
    from { opacity: 0; }    /* Mulai transparan */
    to { opacity: 1; }      /* Berakhir opaque */
}

/* Konten utama modal */
.modal-content {
    background-color: white;
    margin: 5% auto;                                    /* Posisi di tengah dengan margin atas */
    padding: 0;                                         /* Tidak ada padding (diatur per section) */
    border-radius: 15px;                                /* Sudut sangat membulat */
    width: 90%;                                         /* Lebar 90% dari layar */
    max-width: 500px;                                   /* Maksimal 500px */
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);       /* Bayangan dalam dan lebar */
    animation: slideInModal 0.3s ease-out;             /* Animasi masuk */
    overflow: hidden;                                   /* Potong konten yang keluar */
}

/* Animasi slide in untuk modal */
@keyframes slideInModal {
    from {
        opacity: 0;                                     /* Mulai transparan */
        transform: translateY(-50px) scale(0.9);        /* Mulai dari atas dan kecil */
    }
    to {
        opacity: 1;                                     /* Berakhir opaque */
        transform: translateY(0) scale(1);              /* Posisi normal dan ukuran normal */
    }
}

/* Header modal dengan gradient */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;                                      /* Layout horizontal */
    justify-content: space-between;                     /* Judul kiri, tombol close kanan */
    align-items: center;                                /* Sejajarkan vertikal */
}

/* Judul modal */
.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    border: none;                                       /* Override border dari h2 section */
    color: white;
}

/* Tombol close (X) */
.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    width: 30px;                                        /* Ukuran tetap */
    height: 30px;
    border-radius: 50%;                                 /* Bentuk bulat */
    display: flex;                                      /* Untuk center konten */
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;            /* Animasi background */
}

/* Efek hover tombol close */
.close:hover,
.close:focus {
    background-color: rgba(255, 255, 255, 0.2);        /* Background putih transparan */
    text-decoration: none;
}

/* ===========================
   FORM DALAM MODAL
   =========================== */

/* Container form edit buku */
#editBookForm {
    padding: 2rem;                  /* Padding dalam form */
    display: grid;                  /* Layout grid */
    gap: 1.5rem;                   /* Jarak antar field */
}

/* Container setiap field dalam form modal */
#editBookForm div {
    display: flex;
    flex-direction: column;         /* Label di atas, input di bawah */
    gap: 0.5rem;
}

/* Label dalam form modal */
#editBookForm label {
    font-weight: 600;
    color: #555;
    font-size: 0.95rem;            /* Sedikit lebih kecil */
}

/* Input text dan number dalam modal */
#editBookForm input[type="text"],
#editBookForm input[type="number"] {
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;                                 /* Sudut lebih membulat */
    font-size: 1rem;
    transition: all 0.3s ease;                          /* Animasi semua perubahan */
    background-color: #f8f9ff;                          /* Background biru muda */
}

/* Efek focus input dalam modal */
#editBookForm input[type="text"]:focus,
#editBookForm input[type="number"]:focus {
    outline: none;
    border-color: #667eea;                              /* Border biru */
    background-color: white;                            /* Background putih saat focus */
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);    /* Bayangan biru */
}

/* Checkbox dalam modal */
#editBookForm input[type="checkbox"] {
    transform: scale(1.3);                              /* Perbesar checkbox */
    margin-right: 0.5rem;
    accent-color: #667eea;                              /* Warna accent sesuai tema */
}

/* Container checkbox dalam modal - layout horizontal */
#editBookForm div:has(input[type="checkbox"]) {
    flex-direction: row;                                /* Horizontal layout */
    align-items: center;                                /* Sejajarkan vertikal */
    gap: 0.5rem;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-save,
.btn-cancel {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-save {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-save:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-cancel {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Success/Error Messages */
.alert {
    padding: 1rem 1.5rem;
    margin: 1rem 0;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    animation: slideInAlert 0.3s ease-out;
}

@keyframes slideInAlert {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
    border: 1px solid #f1b0b7;
}

/* Statistics Dashboard */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
    border-radius: 10px;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Progress Bar */
.progress-container {
    margin: 1rem 0;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    height: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===========================
   RESPONSIVE DESIGN
   =========================== */

/* Styling untuk layar mobile (tablet dan smartphone) */
@media (max-width: 768px) {
    /* Layout utama menjadi 1 kolom */
    main {
        grid-template-columns: 1fr;    /* Satu kolom penuh */
        margin: 1rem auto;             /* Margin lebih kecil */
        padding: 0 0.5rem;             /* Padding lebih kecil */
    }

    /* Padding section lebih kecil */
    section {
        padding: 1.5rem;
    }

    /* Ukuran font judul lebih kecil */
    header h1 {
        font-size: 2rem;
    }

    /* Tombol header menjadi vertikal */
    .header-actions {
        flex-direction: column;        /* Susun vertikal */
        align-items: center;           /* Pusatkan */
        gap: 0.5rem;                  /* Jarak lebih kecil */
        padding: 0 1rem;              /* Padding samping */
    }

    /* Lebar minimum tombol header */
    .header-actions button {
        min-width: 200px;
    }

    /* Form pencarian menjadi 1 kolom */
    #searchBook {
        grid-template-columns: 1fr;
    }

    /* Tombol pencarian menjadi vertikal */
    #searchBook div:last-child {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Tombol aksi buku menjadi vertikal */
    div[data-testid="bookItem"] > div {
        flex-direction: column;
    }

    /* Tombol aksi buku lebar penuh */
    div[data-testid="bookItem"] > div button {
        width: 100%;
        text-align: center;
    }

    /* Modal lebih besar di mobile */
    .modal-content {
        margin: 10% auto;              /* Margin atas lebih besar */
        width: 95%;                    /* Hampir lebar penuh */
    }

    /* Header modal lebih kecil */
    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    /* Form modal lebih kompak */
    #editBookForm {
        padding: 1.5rem;
        gap: 1rem;
    }

    /* Tombol modal menjadi vertikal */
    .modal-buttons {
        flex-direction: column;
    }

    .btn-save,
    .btn-cancel {
        width: 100%;                   /* Lebar penuh */
    }

    /* Statistik menjadi 1 kolom */
    .stats-container {
        grid-template-columns: 1fr;
    }
}

/* ===========================
   DARK MODE SUPPORT
   =========================== */

/* Styling otomatis untuk dark mode berdasarkan preferensi sistem */
@media (prefers-color-scheme: dark) {
    /* Background dan teks utama */
    body {
        background-color: #1a1a1a;    /* Background hitam */
        color: #e0e0e0;               /* Teks putih */
    }

    /* Section dengan background gelap */
    section {
        background: #2d2d2d;          /* Background abu gelap */
        color: #e0e0e0;
    }

    /* Input dengan tema gelap */
    input[type="text"],
    input[type="number"] {
        background: #3d3d3d;          /* Background input gelap */
        color: #e0e0e0;               /* Teks putih */
        border-color: #555;           /* Border abu */
    }

    /* Item buku dengan tema gelap */
    div[data-testid="bookItem"] {
        background: #3d3d3d;
        color: #e0e0e0;
    }

    /* Modal dengan tema gelap */
    .modal-content {
        background: #2d2d2d;
        color: #e0e0e0;
    }

    /* Kartu statistik dengan tema gelap */
    .stat-card {
        background: #3d3d3d;
        color: #e0e0e0;
    }
}

/* ===========================
   PRINT STYLES
   =========================== */

/* Styling khusus untuk print/cetak */
@media print {
    /* Sembunyikan elemen interaktif saat print */
    header,
    .header-actions,
    button,
    form {
        display: none !important;     /* Paksa sembunyikan */
    }

    /* Section untuk print */
    section {
        box-shadow: none;             /* Hilangkan bayangan */
        border: 1px solid #000;       /* Border hitam */
        margin: 1rem 0;
        page-break-inside: avoid;     /* Hindari potong halaman di tengah section */
    }

    /* Background putih untuk print */
    body {
        background: white;
        color: black;
    }

    /* Item buku untuk print */
    div[data-testid="bookItem"] {
        border: 1px solid #000;       /* Border hitam */
        margin-bottom: 1rem;
    }
}