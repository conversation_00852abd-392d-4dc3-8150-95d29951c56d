* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0 0 1rem 0;
}

/* Header Actions Styling */
.header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.header-actions button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-actions button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.header-actions button:active {
    transform: translateY(0);
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr 1fr;
}

section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

section:first-child,
section:nth-child(2) {
    grid-column: 1 / -1;
}

section h2 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
    position: relative;
}

/* Form Styles */
form {
    display: grid;
    gap: 1rem;
}

form div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

label {
    font-weight: 600;
    color: #555;
}

input[type="text"],
input[type="number"] {
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 0.5rem;
}

button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

#bookFormSubmit,
#searchSubmit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#bookFormSubmit:hover,
#searchSubmit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

#clearSearch {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    margin-left: 0.5rem;
}

#clearSearch:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
}

/* Book Item Styles */
div[data-testid="bookItem"] {
    background: #f8f9ff;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    border-left: 5px solid #667eea;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

div[data-testid="bookItem"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

h3[data-testid="bookItemTitle"] {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

p[data-testid="bookItemAuthor"],
p[data-testid="bookItemYear"] {
    color: #666;
    margin-bottom: 0.3rem;
}

div[data-testid="bookItem"] > div {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

button[data-testid="bookItemIsCompleteButton"] {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemDeleteButton"] {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemEditButton"] {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

button[data-testid="bookItemIsCompleteButton"]:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
    transform: translateY(-1px);
}

button[data-testid="bookItemDeleteButton"]:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    transform: translateY(-1px);
}

button[data-testid="bookItemEditButton"]:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
    transform: translateY(-1px);
}

/* Search Form */
#searchBook {
    grid-template-columns: 1fr auto;
    align-items: end;
    gap: 1rem;
}

#searchBook div {
    flex-direction: column;
}

#searchBook div:last-child {
    flex-direction: row;
    gap: 0.5rem;
}

/* Empty State */
div[data-testid="incompleteBookList"]:empty::after,
div[data-testid="completeBookList"]:empty::after {
    content: "📚 Tidak ada buku dalam kategori ini.";
    display: block;
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 2px dashed #ddd;
    font-size: 1.1rem;
}

/* Form checkbox styling */
form div:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideInModal 0.3s ease-out;
    overflow: hidden;
}

@keyframes slideInModal {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    border: none;
    color: white;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.close:hover,
.close:focus {
    background-color: rgba(255, 255, 255, 0.2);
    text-decoration: none;
}

#editBookForm {
    padding: 2rem;
    display: grid;
    gap: 1.5rem;
}

#editBookForm div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

#editBookForm label {
    font-weight: 600;
    color: #555;
    font-size: 0.95rem;
}

#editBookForm input[type="text"],
#editBookForm input[type="number"] {
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f8f9ff;
}

#editBookForm input[type="text"]:focus,
#editBookForm input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#editBookForm input[type="checkbox"] {
    transform: scale(1.3);
    margin-right: 0.5rem;
    accent-color: #667eea;
}

/* Modal checkbox container */
#editBookForm div:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-save,
.btn-cancel {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-save {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-save:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-cancel {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Success/Error Messages */
.alert {
    padding: 1rem 1.5rem;
    margin: 1rem 0;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    animation: slideInAlert 0.3s ease-out;
}

@keyframes slideInAlert {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
    border: 1px solid #f1b0b7;
}

/* Statistics Dashboard */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
    border-radius: 10px;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Progress Bar */
.progress-container {
    margin: 1rem 0;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    height: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    section {
        padding: 1.5rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .header-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 0 1rem;
    }
    
    .header-actions button {
        min-width: 200px;
    }
    
    #searchBook {
        grid-template-columns: 1fr;
    }
    
    #searchBook div:last-child {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    div[data-testid="bookItem"] > div {
        flex-direction: column;
    }
    
    div[data-testid="bookItem"] > div button {
        width: 100%;
        text-align: center;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal-header {
        padding: 1rem 1.5rem;
    }
    
    .modal-header h2 {
        font-size: 1.3rem;
    }
    
    #editBookForm {
        padding: 1.5rem;
        gap: 1rem;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    .btn-save,
    .btn-cancel {
        width: 100%;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    section {
        background: #2d2d2d;
        color: #e0e0e0;
    }
    
    input[type="text"],
    input[type="number"] {
        background: #3d3d3d;
        color: #e0e0e0;
        border-color: #555;
    }
    
    div[data-testid="bookItem"] {
        background: #3d3d3d;
        color: #e0e0e0;
    }
    
    .modal-content {
        background: #2d2d2d;
        color: #e0e0e0;
    }
    
    .stat-card {
        background: #3d3d3d;
        color: #e0e0e0;
    }
}

/* Print Styles */
@media print {
    header,
    .header-actions,
    button,
    form {
        display: none !important;
    }
    
    section {
        box-shadow: none;
        border: 1px solid #000;
        margin: 1rem 0;
        page-break-inside: avoid;
    }
    
    body {
        background: white;
        color: black;
    }
    
    div[data-testid="bookItem"] {
        border: 1px solid #000;
        margin-bottom: 1rem;
    }
}