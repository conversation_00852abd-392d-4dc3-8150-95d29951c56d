# Bookshelf App - Aplikasi Rak Buku Digital

Aplikasi web untuk mengelola koleksi buku pribadi dengan fitur lengkap untuk menambah, mengedit, menghapus, dan mencari buku.

## Fitur Utama

- ✅ **Tambah Buku Baru**: Menambahkan buku dengan informasi judul, penulis, tahun, dan status baca
- ✅ **Kelola Status Buku**: Memindahkan buku antara "Belum selesai dibaca" dan "Selesai dibaca"
- ✅ **Edit Buku**: Mengubah informasi buku yang sudah ada
- ✅ **Hapus Buku**: Menghapus buku dari koleksi
- ✅ **Pencarian**: Mencari buku berdasarkan judul atau penulis
- ✅ **Sorting**: Mengurutkan buku berdasarkan abjad (A-Z atau Z-A)
- ✅ **Export/Import**: Backup dan restore data buku dalam format JSON
- ✅ **Penyimpanan Lokal**: Data tersimpan otomatis di browser
- ✅ **Responsive Design**: Tampilan optimal di desktop dan mobile
- ✅ **Validasi Form**: Validasi input dan pencegahan duplikasi
- ✅ **Counter Buku**: Menampilkan jumlah buku di setiap kategori

## Cara Menggunakan

1. **Menambah Buku**: Isi form "Tambah Buku Baru" dan klik tombol submit
2. **Mencari Buku**: Gunakan form pencarian untuk mencari berdasarkan judul atau penulis
3. **Mengurutkan**: Klik tombol "Urutkan A-Z" di header untuk mengurutkan buku
4. **Export Data**: Klik "Export Data" untuk mengunduh backup data
5. **Import Data**: Klik "Import Data" untuk memulihkan data dari file backup

## Teknologi yang Digunakan

- **HTML5**: Struktur aplikasi
- **CSS3**: Styling dengan gradient, animasi, dan responsive design
- **JavaScript (ES6+)**: Logika aplikasi dan manipulasi DOM
- **LocalStorage**: Penyimpanan data lokal

## Ketentuan Pengerjaan Tugas

Untuk mempermudah penilaian submission yang dikirim, Anda perlu memahami ketentuan-ketentuan berikut dalam mengerjakan tugas ini.

- Anda dilarang mengedit atau menghapus atribut `data-testid` pada elemen-elemen HTML.
- Ini masih berkaitan dengan poin sebelumnya. Jika Anda memiliki kebutuhan seperti styling elemen dan perlu menambahkan atribut seperti class, itu tidak dilarang selama atribut `data-testid` beserta nilainya tidak diubah atau dihapus.
- Dalam menampilkan data-data buku, Anda wajib memberikan beberapa atribut pada setiap elemennya.

  - `data-bookid`: menampung nilai ID masing-masing buku.
  - `data-testid`: penanda jenis data buku yang ditampilkan. Berikut daftarnya.
    - `bookItem`: elemen kontainer yang menampung data-data buku.
    - `bookItemTitle`: judul buku
    - `bookItemAuthor`: penulis buku
    - `bookItemYear`: tahun rilis buku
    - `bookItemIsCompleteButton`: tombol untuk mengubah kondisi buku dari “Belum selesai dibaca” menjadi “Selesai dibaca” atau sebaliknya.
    - `bookItemDeleteButton`: tombol untuk menghapus buku.
    - `bookItemEditButton`: tombol untuk mengubah data buku.

  Agar pengerjaan tugas lebih mudah, Anda dapat mengikuti templat buku berikut.

```html
<div data-bookid="{{ ID_buku }}" data-testid="bookItem">
  <h3 data-testid="bookItemTitle">{{ judul_buku }}</h3>
  <p data-testid="bookItemAuthor">Penulis: {{ penulis_buku }}</p>
  <p data-testid="bookItemYear">Tahun: {{ tahun_rilis_buku }}</p>
  <div>
    <button data-testid="bookItemIsCompleteButton">{{ tombol_untuk_ubah_kondisi }}</button>
    <button data-testid="bookItemDeleteButton">{{ tombol_untuk_hapus }}</button>
    <button data-testid="bookItemEditButton">{{ tombol_untuk_edit }}</button>
  </div>
</div>
```

Selamat mengerjakan dan sukses selalu!
