// Do your work here...
console.log('Hello, world!');

// Storage key for localStorage
const STORAGE_KEY = 'BOOKSHELF_APPS';

// Global books array
let books = [];

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    loadDataFromStorage();
    renderBookList();
    
    // Event listeners
    document.getElementById('bookForm').addEventListener('submit', addBook);
    document.getElementById('searchBook').addEventListener('submit', searchBook);
    document.getElementById('bookFormIsComplete').addEventListener('change', updateSubmitButtonText);
});

// Load data from localStorage
function loadDataFromStorage() {
    const serializedData = localStorage.getItem(STORAGE_KEY);
    if (serializedData !== null) {
        books = JSON.parse(serializedData);
    }
}

// Save data to localStorage
function saveDataToStorage() {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(books));
}

// Generate unique ID using timestamp
function generateId() {
    return +new Date();
}

// Update submit button text based on checkbox
function updateSubmitButtonText() {
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    const submitButton = document.getElementById('bookFormSubmit');
    const span = submitButton.querySelector('span');
    
    span.textContent = isComplete ? 'Selesai dibaca' : 'Belum selesai dibaca';
}

// Add new book
function addBook(event) {
    event.preventDefault();
    
    const title = document.getElementById('bookFormTitle').value;
    const author = document.getElementById('bookFormAuthor').value;
    const year = parseInt(document.getElementById('bookFormYear').value);
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    
    const bookObject = {
        id: generateId(),
        title: title,
        author: author,
        year: year,
        isComplete: isComplete
    };
    
    books.push(bookObject);
    saveDataToStorage();
    renderBookList();
    
    // Reset form
    document.getElementById('bookForm').reset();
    updateSubmitButtonText();
    
    alert('Buku berhasil ditambahkan!');
}

// Create book element
function createBookElement(bookObject) {
    const bookItem = document.createElement('div');
    bookItem.setAttribute('data-bookid', bookObject.id);
    bookItem.setAttribute('data-testid', 'bookItem');
    
    const bookTitle = document.createElement('h3');
    bookTitle.setAttribute('data-testid', 'bookItemTitle');
    bookTitle.textContent = bookObject.title;
    
    const bookAuthor = document.createElement('p');
    bookAuthor.setAttribute('data-testid', 'bookItemAuthor');
    bookAuthor.textContent = `Penulis: ${bookObject.author}`;
    
    const bookYear = document.createElement('p');
    bookYear.setAttribute('data-testid', 'bookItemYear');
    bookYear.textContent = `Tahun: ${bookObject.year}`;
    
    const buttonContainer = document.createElement('div');
    
    // Toggle complete button
    const toggleButton = document.createElement('button');
    toggleButton.setAttribute('data-testid', 'bookItemIsCompleteButton');
    toggleButton.textContent = bookObject.isComplete ? 'Belum selesai dibaca' : 'Selesai dibaca';
    toggleButton.addEventListener('click', function() {
        toggleBookComplete(bookObject.id);
    });
    
    // Delete button
    const deleteButton = document.createElement('button');
    deleteButton.setAttribute('data-testid', 'bookItemDeleteButton');
    deleteButton.textContent = 'Hapus Buku';
    deleteButton.addEventListener('click', function() {
        deleteBook(bookObject.id);
    });
    
    // Edit button
    const editButton = document.createElement('button');
    editButton.setAttribute('data-testid', 'bookItemEditButton');
    editButton.textContent = 'Edit Buku';
    editButton.addEventListener('click', function() {
        editBook(bookObject.id);
    });
    
    buttonContainer.append(toggleButton, deleteButton, editButton);
    bookItem.append(bookTitle, bookAuthor, bookYear, buttonContainer);
    
    return bookItem;
}

// Render book list
function renderBookList(filteredBooks = null) {
    const incompleteBookList = document.getElementById('incompleteBookList');
    const completeBookList = document.getElementById('completeBookList');
    
    // Clear existing content
    incompleteBookList.innerHTML = '';
    completeBookList.innerHTML = '';
    
    const booksToRender = filteredBooks || books;
    
    for (const book of booksToRender) {
        const bookElement = createBookElement(book);
        
        if (book.isComplete) {
            completeBookList.appendChild(bookElement);
        } else {
            incompleteBookList.appendChild(bookElement);
        }
    }
}

// Toggle book complete status
function toggleBookComplete(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        books[bookTarget].isComplete = !books[bookTarget].isComplete;
        saveDataToStorage();
        renderBookList();
    }
}

// Delete book
function deleteBook(bookId) {
    if (confirm('Apakah Anda yakin ingin menghapus buku ini?')) {
        const bookTarget = findBookIndex(bookId);
        if (bookTarget !== -1) {
            books.splice(bookTarget, 1);
            saveDataToStorage();
            renderBookList();
            alert('Buku berhasil dihapus!');
        }
    }
}

// Edit book
function editBook(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        const book = books[bookTarget];
        
        const newTitle = prompt('Masukkan judul baru:', book.title);
        if (newTitle === null) return; // User cancelled
        
        const newAuthor = prompt('Masukkan penulis baru:', book.author);
        if (newAuthor === null) return; // User cancelled
        
        const newYear = prompt('Masukkan tahun baru:', book.year);
        if (newYear === null) return; // User cancelled
        
        // Validate year
        const yearNum = parseInt(newYear);
        if (isNaN(yearNum)) {
            alert('Tahun harus berupa angka!');
            return;
        }
        
        // Update book data
        books[bookTarget].title = newTitle.trim();
        books[bookTarget].author = newAuthor.trim();
        books[bookTarget].year = yearNum;
        
        saveDataToStorage();
        renderBookList();
        alert('Buku berhasil diedit!');
    }
}

// Find book index by ID
function findBookIndex(bookId) {
    return books.findIndex(book => book.id === bookId);
}

// Search book functionality
function searchBook(event) {
    event.preventDefault();
    
    const searchTitle = document.getElementById('searchBookTitle').value.toLowerCase();
    
    if (searchTitle.trim() === '') {
        renderBookList(); // Show all books if search is empty
        return;
    }
    
    const filteredBooks = books.filter(book => 
        book.title.toLowerCase().includes(searchTitle)
    );
    
    renderBookList(filteredBooks);
    
    if (filteredBooks.length === 0) {
        alert('Tidak ada buku yang ditemukan dengan judul tersebut.');
    }
}