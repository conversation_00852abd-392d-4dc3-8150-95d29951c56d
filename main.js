// Storage key for localStorage
const STORAGE_KEY = 'BOOKSHELF_APPS';

// Global books array
let books = [];

// Sort state
let sortAscending = true;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    loadDataFromStorage();
    renderBookList();
    updateBookCounters();
    
    // Event listeners
    document.getElementById('bookForm').addEventListener('submit', addBook);
    document.getElementById('searchBook').addEventListener('submit', searchBook);
    document.getElementById('clearSearch').addEventListener('click', clearSearch);
    document.getElementById('bookFormIsComplete').addEventListener('change', updateSubmitButtonText);
    
    // New event listeners for additional features
    document.getElementById('sortBooks').addEventListener('click', sortBooks);
    document.getElementById('exportData').addEventListener('click', exportData);
    document.getElementById('importButton').addEventListener('click', () => {
        document.getElementById('importData').click();
    });
    document.getElementById('importData').addEventListener('change', importData);
});

// Load data from localStorage
function loadDataFromStorage() {
    const serializedData = localStorage.getItem(STORAGE_KEY);
    if (serializedData !== null) {
        books = JSON.parse(serializedData);
    }
}

// Save data to localStorage
function saveDataToStorage() {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(books));
}

// Generate unique ID using timestamp
function generateId() {
    return +new Date();
}

// Update submit button text based on checkbox
function updateSubmitButtonText() {
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    const submitButton = document.getElementById('bookFormSubmit');
    const span = submitButton.querySelector('span');
    
    span.textContent = isComplete ? 'Selesai dibaca' : 'Belum selesai dibaca';
}

// Add new book
function addBook(event) {
    event.preventDefault();
    
    const title = document.getElementById('bookFormTitle').value.trim();
    const author = document.getElementById('bookFormAuthor').value.trim();
    const year = parseInt(document.getElementById('bookFormYear').value);
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    
    // Validation
    if (!title || !author || isNaN(year)) {
        alert('Mohon isi semua field dengan benar!');
        return;
    }
    
    const bookObject = {
        id: generateId(),
        title: title,
        author: author,
        year: year,
        isComplete: isComplete
    };
    
    books.push(bookObject);
    saveDataToStorage();
    renderBookList();
    updateBookCounters();
    
    // Reset form
    document.getElementById('bookForm').reset();
    updateSubmitButtonText();
    
    alert('Buku berhasil ditambahkan!');
}

// Create book element
function createBookElement(bookObject) {
    const bookItem = document.createElement('div');
    bookItem.setAttribute('data-bookid', bookObject.id);
    bookItem.setAttribute('data-testid', 'bookItem');
    
    const bookTitle = document.createElement('h3');
    bookTitle.setAttribute('data-testid', 'bookItemTitle');
    bookTitle.textContent = bookObject.title;
    
    const bookAuthor = document.createElement('p');
    bookAuthor.setAttribute('data-testid', 'bookItemAuthor');
    bookAuthor.textContent = `Penulis: ${bookObject.author}`;
    
    const bookYear = document.createElement('p');
    bookYear.setAttribute('data-testid', 'bookItemYear');
    bookYear.textContent = `Tahun: ${bookObject.year}`;
    
    const buttonContainer = document.createElement('div');
    
    // Toggle complete button
    const toggleButton = document.createElement('button');
    toggleButton.setAttribute('data-testid', 'bookItemIsCompleteButton');
    toggleButton.textContent = bookObject.isComplete ? 'Belum selesai dibaca' : 'Selesai dibaca';
    toggleButton.addEventListener('click', function() {
        toggleBookComplete(bookObject.id);
    });
    
    // Delete button
    const deleteButton = document.createElement('button');
    deleteButton.setAttribute('data-testid', 'bookItemDeleteButton');
    deleteButton.textContent = 'Hapus Buku';
    deleteButton.addEventListener('click', function() {
        deleteBook(bookObject.id);
    });
    
    // Edit button
    const editButton = document.createElement('button');
    editButton.setAttribute('data-testid', 'bookItemEditButton');
    editButton.textContent = 'Edit Buku';
    editButton.addEventListener('click', function() {
        editBook(bookObject.id);
    });
    
    buttonContainer.append(toggleButton, deleteButton, editButton);
    bookItem.append(bookTitle, bookAuthor, bookYear, buttonContainer);
    
    return bookItem;
}

// Render book list
function renderBookList(filteredBooks = null) {
    const incompleteBookList = document.getElementById('incompleteBookList');
    const completeBookList = document.getElementById('completeBookList');
    
    // Clear existing content
    incompleteBookList.innerHTML = '';
    completeBookList.innerHTML = '';
    
    const booksToRender = filteredBooks || books;
    
    for (const book of booksToRender) {
        const bookElement = createBookElement(book);
        
        if (book.isComplete) {
            completeBookList.appendChild(bookElement);
        } else {
            incompleteBookList.appendChild(bookElement);
        }
    }
}

// Update book counters
function updateBookCounters() {
    const incompleteCount = books.filter(book => !book.isComplete).length;
    const completeCount = books.filter(book => book.isComplete).length;
    
    // Update section headers with counters
    const incompleteSectionTitle = document.querySelector('section:nth-child(3) h2');
    const completeSectionTitle = document.querySelector('section:nth-child(4) h2');
    
    incompleteSectionTitle.textContent = `Belum selesai dibaca (${incompleteCount})`;
    completeSectionTitle.textContent = `Selesai dibaca (${completeCount})`;
}

// Toggle book complete status
function toggleBookComplete(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        books[bookTarget].isComplete = !books[bookTarget].isComplete;
        saveDataToStorage();
        renderBookList();
        updateBookCounters();
    }
}

// Delete book
function deleteBook(bookId) {
    if (confirm('Apakah Anda yakin ingin menghapus buku ini?')) {
        const bookTarget = findBookIndex(bookId);
        if (bookTarget !== -1) {
            books.splice(bookTarget, 1);
            saveDataToStorage();
            renderBookList();
            updateBookCounters();
            alert('Buku berhasil dihapus!');
        }
    }
}

// Edit book
function editBook(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        const book = books[bookTarget];
        showEditModal(book);
    }
}

// Show edit modal
function showEditModal(book) {
    // Create modal HTML
    const modalHTML = `
        <div id="editModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Edit Buku</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="editBookForm">
                    <div>
                        <label for="editTitle">Judul</label>
                        <input id="editTitle" type="text" value="${book.title}" required />
                    </div>
                    <div>
                        <label for="editAuthor">Penulis</label>
                        <input id="editAuthor" type="text" value="${book.author}" required />
                    </div>
                    <div>
                        <label for="editYear">Tahun</label>
                        <input id="editYear" type="number" value="${book.year}" required />
                    </div>
                    <div>
                        <label for="editIsComplete">Selesai dibaca</label>
                        <input id="editIsComplete" type="checkbox" ${book.isComplete ? 'checked' : ''} />
                    </div>
                    <div class="modal-buttons">
                        <button type="submit" class="btn-save">Simpan Perubahan</button>
                        <button type="button" class="btn-cancel">Batal</button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Get modal elements
    const modal = document.getElementById('editModal');
    const form = document.getElementById('editBookForm');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.btn-cancel');
    
    // Show modal
    modal.style.display = 'block';
    
    // Close modal events
    closeBtn.addEventListener('click', closeEditModal);
    cancelBtn.addEventListener('click', closeEditModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeEditModal();
        }
    });
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const newTitle = document.getElementById('editTitle').value.trim();
        const newAuthor = document.getElementById('editAuthor').value.trim();
        const newYear = parseInt(document.getElementById('editYear').value);
        const newIsComplete = document.getElementById('editIsComplete').checked;
        
        // Validate inputs
        if (!newTitle || !newAuthor || isNaN(newYear)) {
            alert('Mohon isi semua field dengan benar!');
            return;
        }
        
        // Update book data
        const bookTarget = findBookIndex(book.id);
        if (bookTarget !== -1) {
            books[bookTarget].title = newTitle;
            books[bookTarget].author = newAuthor;
            books[bookTarget].year = newYear;
            books[bookTarget].isComplete = newIsComplete;
            
            saveDataToStorage();
            renderBookList();
            updateBookCounters();
            closeEditModal();
            alert('Buku berhasil diedit!');
        }
    });
}

// Close edit modal
function closeEditModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.remove();
    }
}

// Find book index by ID
function findBookIndex(bookId) {
    return books.findIndex(book => book.id === bookId);
}

// Search book functionality
function searchBook(event) {
    event.preventDefault();
    
    const searchTitle = document.getElementById('searchBookTitle').value.toLowerCase().trim();
    
    if (searchTitle === '') {
        renderBookList(); // Show all books if search is empty
        return;
    }
    
    const filteredBooks = books.filter(book => 
        book.title.toLowerCase().includes(searchTitle) ||
        book.author.toLowerCase().includes(searchTitle)
    );
    
    renderBookList(filteredBooks);
    
    if (filteredBooks.length === 0) {
        alert('Tidak ada buku yang ditemukan dengan kata kunci tersebut.');
    }
}

// Clear search
function clearSearch() {
    document.getElementById('searchBookTitle').value = '';
    renderBookList();
}

// Sort books alphabetically
function sortBooks() {
    const sortButton = document.getElementById('sortBooks');
    
    books.sort((a, b) => {
        if (sortAscending) {
            return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
        } else {
            return b.title.toLowerCase().localeCompare(a.title.toLowerCase());
        }
    });
    
    // Toggle sort direction
    sortAscending = !sortAscending;
    sortButton.textContent = sortAscending ? 'Urutkan A-Z' : 'Urutkan Z-A';
    
    saveDataToStorage();
    renderBookList();
}

// Export data to JSON
function exportData() {
    if (books.length === 0) {
        alert('Tidak ada data buku untuk diekspor!');
        return;
    }
    
    const dataStr = JSON.stringify(books, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `bookshelf-backup-${new Date().toISOString().split('T')[0]}.json`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    alert('Data berhasil diekspor!');
}

// Import data from JSON
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.endsWith('.json')) {
        alert('File harus berformat JSON!');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);
            
            // Validate imported data
            if (!Array.isArray(importedData)) {
                throw new Error('Format data tidak valid!');
            }
            
            // Validate each book object
            for (const book of importedData) {
                if (!book.hasOwnProperty('id') || 
                    !book.hasOwnProperty('title') || 
                    !book.hasOwnProperty('author') || 
                    !book.hasOwnProperty('year') || 
                    !book.hasOwnProperty('isComplete')) {
                    throw new Error('Format buku tidak valid!');
                }
            }
            
            // Confirm import
            const confirmImport = confirm(
                `Ditemukan ${importedData.length} buku untuk diimpor.\n` +
                'Data yang sudah ada akan digabung dengan data baru.\n' +
                'Lanjutkan import?'
            );
            
            if (confirmImport) {
                // Merge with existing data
                const existingIds = books.map(book => book.id);
                const newBooks = importedData.filter(book => !existingIds.includes(book.id));
                
                books = books.concat(newBooks);
                saveDataToStorage();
                renderBookList();
                updateBookCounters();
                
                alert(`${newBooks.length} buku berhasil diimpor!`);
            }
            
        } catch (error) {
            alert('Gagal mengimpor data: ' + error.message);
        }
        
        // Reset file input
        event.target.value = '';
    };
    
    reader.readAsText(file);
}