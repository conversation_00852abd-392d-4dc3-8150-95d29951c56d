// Storage key for localStorage
const STORAGE_KEY = 'BOOKSHELF_APPS';

// Global books array
let books = [];

// Sort state
let sortAscending = true;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    loadDataFromStorage();
    renderBookList();
    updateBookCounters();
    
    // Event listeners
    document.getElementById('bookForm').addEventListener('submit', addBook);
    document.getElementById('searchBook').addEventListener('submit', searchBook);
    document.getElementById('clearSearch').addEventListener('click', clearSearch);
    document.getElementById('bookFormIsComplete').addEventListener('change', updateSubmitButtonText);
    
    // New event listeners for additional features
    document.getElementById('sortBooks').addEventListener('click', sortBooks);
    document.getElementById('exportData').addEventListener('click', exportData);
    document.getElementById('importButton').addEventListener('click', () => {
        document.getElementById('importData').click();
    });
    document.getElementById('importData').addEventListener('change', importData);
});

// Load data from localStorage
function loadDataFromStorage() {
    const serializedData = localStorage.getItem(STORAGE_KEY);
    if (serializedData !== null) {
        books = JSON.parse(serializedData);
    }
}

// Save data to localStorage
function saveDataToStorage() {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(books));
}

// Generate unique ID using timestamp
function generateId() {
    return +new Date();
}

// Update submit button text based on checkbox
function updateSubmitButtonText() {
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    const submitButton = document.getElementById('bookFormSubmit');
    const span = submitButton.querySelector('span');
    
    span.textContent = isComplete ? 'Selesai dibaca' : 'Belum selesai dibaca';
}

// Add new book
function addBook(event) {
    event.preventDefault();
    
    const title = document.getElementById('bookFormTitle').value.trim();
    const author = document.getElementById('bookFormAuthor').value.trim();
    const year = parseInt(document.getElementById('bookFormYear').value);
    const isComplete = document.getElementById('bookFormIsComplete').checked;
    
    // Validation
    if (!title || !author || isNaN(year)) {
        alert('Mohon isi semua field dengan benar!');
        return;
    }
    
    const bookObject = {
        id: generateId(),
        title: title,
        author: author,
        year: year,
        isComplete: isComplete
    };
    
    books.push(bookObject);
    saveDataToStorage();
    renderBookList();
    updateBookCounters();
    
    // Reset form
    document.getElementById('bookForm').reset();
    updateSubmitButtonText();
    
    alert('Buku berhasil ditambahkan!');
}

// Create book element
function createBookElement(bookObject) {
    const bookItem = document.createElement('div');
    bookItem.setAttribute('data-bookid', bookObject.id);
    bookItem.setAttribute('data-testid', 'bookItem');
    
    const bookTitle = document.createElement('h3');
    bookTitle.setAttribute('data-testid', 'bookItemTitle');
    bookTitle.textContent = bookObject.title;
    
    const bookAuthor = document.createElement('p');
    bookAuthor.setAttribute('data-testid', 'bookItemAuthor');
    bookAuthor.textContent = `Penulis: ${bookObject.author}`;
    
    const bookYear = document.createElement('p');
    bookYear.setAttribute('data-testid', 'bookItemYear');
    bookYear.textContent = `Tahun: ${bookObject.year}`;
    
    const buttonContainer = document.createElement('div');
    
    // Toggle complete button
    const toggleButton = document.createElement('button');
    toggleButton.setAttribute('data-testid', 'bookItemIsCompleteButton');
    toggleButton.textContent = bookObject.isComplete ? 'Belum selesai dibaca' : 'Selesai dibaca';
    toggleButton.addEventListener('click', function() {
        toggleBookComplete(bookObject.id);
    });
    
    // Delete button
    const deleteButton = document.createElement('button');
    deleteButton.setAttribute('data-testid', 'bookItemDeleteButton');
    deleteButton.textContent = 'Hapus Buku';
    deleteButton.addEventListener('click', function() {
        deleteBook(bookObject.id);
    });
    
    // Edit button
    const editButton = document.createElement('button');
    editButton.setAttribute('data-testid', 'bookItemEditButton');
    editButton.textContent = 'Edit Buku';
    editButton.addEventListener('click', function() {
        editBook(bookObject.id);
    });
    
    buttonContainer.append(toggleButton, deleteButton, editButton);
    bookItem.append(bookTitle, bookAuthor, bookYear, buttonContainer);
    
    return bookItem;
}

// Render book list
function renderBookList(filteredBooks = null) {
    const incompleteBookList = document.getElementById('incompleteBookList');
    const completeBookList = document.getElementById('completeBookList');
    
    // Clear existing content
    incompleteBookList.innerHTML = '';
    completeBookList.innerHTML = '';
    
    const booksToRender = filteredBooks || books;
    
    for (const book of booksToRender) {
        const bookElement = createBookElement(book);
        
        if (book.isComplete) {
            completeBookList.appendChild(bookElement);
        } else {
            incompleteBookList.appendChild(bookElement);
        }
    }
}

// Update book counters
function updateBookCounters() {
    const incompleteCount = books.filter(book => !book.isComplete).length;
    const completeCount = books.filter(book => book.isComplete).length;
    
    // Update section headers with counters
    const incompleteSectionTitle = document.querySelector('section:nth-child(3) h2');
    const completeSectionTitle = document.querySelector('section:nth-child(4) h2');
    
    incompleteSectionTitle.textContent = `Belum selesai dibaca (${incompleteCount})`;
    completeSectionTitle.textContent = `Selesai dibaca (${completeCount})`;
}

// Toggle book complete status
function toggleBookComplete(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        books[bookTarget].isComplete = !books[bookTarget].isComplete;
        saveDataToStorage();
        renderBookList();
        updateBookCounters();
    }
}

// Delete book
function deleteBook(bookId) {
    if (confirm('Apakah Anda yakin ingin menghapus buku ini?')) {
        const bookTarget = findBookIndex(bookId);
        if (bookTarget !== -1) {
            books.splice(bookTarget, 1);
            saveDataToStorage();
            renderBookList();
            updateBookCounters();
            alert('Buku berhasil dihapus!');
        }
    }
}

// Edit book
function editBook(bookId) {
    const bookTarget = findBookIndex(bookId);
    if (bookTarget !== -1) {
        const book = books[bookTarget];
        showEditModal(book);
    }
}

/**
 * FUNGSI MODAL EDIT BUKU
 * ======================
 */

/**
 * Menampilkan modal untuk mengedit buku
 * @param {Object} book - Objek buku yang akan diedit
 */
function showEditModal(book) {
    // Membuat HTML untuk modal edit
    const modalHTML = `
        <div id="editModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Edit Buku</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="editBookForm">
                    <div>
                        <label for="editTitle">Judul</label>
                        <input id="editTitle" type="text" value="${book.title}" required />
                    </div>
                    <div>
                        <label for="editAuthor">Penulis</label>
                        <input id="editAuthor" type="text" value="${book.author}" required />
                    </div>
                    <div>
                        <label for="editYear">Tahun</label>
                        <input id="editYear" type="number" value="${book.year}" required />
                    </div>
                    <div>
                        <label for="editIsComplete">Selesai dibaca</label>
                        <input id="editIsComplete" type="checkbox" ${book.isComplete ? 'checked' : ''} />
                    </div>
                    <div class="modal-buttons">
                        <button type="submit" class="btn-save">Simpan Perubahan</button>
                        <button type="button" class="btn-cancel">Batal</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Menambahkan modal ke dalam body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Mengambil elemen-elemen modal
    const modal = document.getElementById('editModal');
    const form = document.getElementById('editBookForm');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.btn-cancel');

    // Menampilkan modal
    modal.style.display = 'block';

    // Event untuk menutup modal
    closeBtn.addEventListener('click', closeEditModal);
    cancelBtn.addEventListener('click', closeEditModal);

    // Menutup modal jika user klik di luar modal
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeEditModal();
        }
    });

    // Menangani submit form edit
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Mengambil nilai dari form
        const newTitle = document.getElementById('editTitle').value.trim();
        const newAuthor = document.getElementById('editAuthor').value.trim();
        const newYear = parseInt(document.getElementById('editYear').value);
        const newIsComplete = document.getElementById('editIsComplete').checked;

        // Validasi input
        if (!newTitle || !newAuthor || isNaN(newYear)) {
            alert('Mohon isi semua field dengan benar!');
            return;
        }

        // Memperbarui data buku
        const bookTarget = findBookIndex(book.id);
        if (bookTarget !== -1) {
            books[bookTarget].title = newTitle;
            books[bookTarget].author = newAuthor;
            books[bookTarget].year = newYear;
            books[bookTarget].isComplete = newIsComplete;

            // Simpan perubahan dan perbarui tampilan
            saveDataToStorage();
            renderBookList();
            updateBookCounters();
            closeEditModal();

            alert('Buku berhasil diedit!');
        }
    });
}

/**
 * Menutup dan menghapus modal edit dari DOM
 */
function closeEditModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * FUNGSI PENCARIAN DAN UTILITAS
 * =============================
 */

/**
 * Mencari index buku berdasarkan ID
 * @param {number} bookId - ID buku yang dicari
 * @returns {number} Index buku dalam array, atau -1 jika tidak ditemukan
 */
function findBookIndex(bookId) {
    return books.findIndex(book => book.id === bookId);
}

/**
 * FUNGSI PENCARIAN BUKU
 * =====================
 */

/**
 * Mencari buku berdasarkan judul atau penulis
 * @param {Event} event - Event dari form submit
 */
function searchBook(event) {
    event.preventDefault();

    // Mengambil kata kunci pencarian dan mengubah ke huruf kecil
    const searchTitle = document.getElementById('searchBookTitle').value.toLowerCase().trim();

    // Jika pencarian kosong, tampilkan semua buku
    if (searchTitle === '') {
        renderBookList();
        return;
    }

    // Filter buku berdasarkan judul atau penulis yang mengandung kata kunci
    const filteredBooks = books.filter(book =>
        book.title.toLowerCase().includes(searchTitle) ||
        book.author.toLowerCase().includes(searchTitle)
    );

    // Tampilkan hasil pencarian
    renderBookList(filteredBooks);

    // Notifikasi jika tidak ada hasil
    if (filteredBooks.length === 0) {
        alert('Tidak ada buku yang ditemukan dengan kata kunci tersebut.');
    }
}

/**
 * Membersihkan pencarian dan menampilkan semua buku
 */
function clearSearch() {
    document.getElementById('searchBookTitle').value = '';
    renderBookList();
}

/**
 * FUNGSI SORTING DAN EXPORT/IMPORT
 * ================================
 */

/**
 * Mengurutkan buku berdasarkan abjad judul
 * Bergantian antara A-Z dan Z-A setiap kali dipanggil
 */
function sortBooks() {
    const sortButton = document.getElementById('sortBooks');

    // Mengurutkan array books berdasarkan judul
    books.sort((a, b) => {
        if (sortAscending) {
            // Urutan A-Z
            return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
        } else {
            // Urutan Z-A
            return b.title.toLowerCase().localeCompare(a.title.toLowerCase());
        }
    });

    // Mengubah status sorting dan teks tombol
    sortAscending = !sortAscending;
    sortButton.textContent = sortAscending ? 'Urutkan A-Z' : 'Urutkan Z-A';

    // Simpan perubahan dan perbarui tampilan
    saveDataToStorage();
    renderBookList();
}

/**
 * Mengekspor data buku ke file JSON
 * File akan diunduh otomatis dengan nama berisi tanggal
 */
function exportData() {
    // Cek apakah ada data untuk diekspor
    if (books.length === 0) {
        alert('Tidak ada data buku untuk diekspor!');
        return;
    }

    // Mengubah array books menjadi JSON string dengan format yang rapi
    const dataStr = JSON.stringify(books, null, 2);

    // Membuat blob untuk file download
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    // Membuat link download sementara
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `bookshelf-backup-${new Date().toISOString().split('T')[0]}.json`;

    // Menambahkan link ke DOM, klik, lalu hapus
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('Data berhasil diekspor!');
}

/**
 * Mengimpor data buku dari file JSON
 * @param {Event} event - Event dari input file
 */
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validasi ekstensi file
    if (!file.name.endsWith('.json')) {
        alert('File harus berformat JSON!');
        return;
    }

    // Membaca file menggunakan FileReader
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            // Parse JSON dari file
            const importedData = JSON.parse(e.target.result);

            // Validasi: pastikan data adalah array
            if (!Array.isArray(importedData)) {
                throw new Error('Format data tidak valid!');
            }

            // Validasi setiap objek buku
            for (const book of importedData) {
                if (!book.hasOwnProperty('id') ||
                    !book.hasOwnProperty('title') ||
                    !book.hasOwnProperty('author') ||
                    !book.hasOwnProperty('year') ||
                    !book.hasOwnProperty('isComplete')) {
                    throw new Error('Format buku tidak valid!');
                }
            }

            // Konfirmasi import dari user
            const confirmImport = confirm(
                `Ditemukan ${importedData.length} buku untuk diimpor.\n` +
                'Data yang sudah ada akan digabung dengan data baru.\n' +
                'Lanjutkan import?'
            );

            if (confirmImport) {
                // Gabungkan dengan data yang sudah ada (hindari duplikasi berdasarkan ID)
                const existingIds = books.map(book => book.id);
                const newBooks = importedData.filter(book => !existingIds.includes(book.id));

                // Tambahkan buku baru ke array
                books = books.concat(newBooks);

                // Simpan dan perbarui tampilan
                saveDataToStorage();
                renderBookList();
                updateBookCounters();

                alert(`${newBooks.length} buku berhasil diimpor!`);
            }

        } catch (error) {
            alert('Gagal mengimpor data: ' + error.message);
        }

        // Reset input file agar bisa memilih file yang sama lagi
        event.target.value = '';
    };

    // Mulai membaca file sebagai text
    reader.readAsText(file);
}