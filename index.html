<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bookshelf App</title>
    <link rel="stylesheet" href="style.css" />

    <!-- Impor CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- Impor script kamu di sini -->
    <!-- <PERSON><PERSON><PERSON><PERSON> `defer` jika pemanggilan dilakukan dalam <head> -->
    <script defer src="main.js"></script>
  </head>
  <body>
    <header>
      <h1>Bookshelf App</h1>
      <div class="header-actions">
        <button id="sortBooks" type="button">Urutkan A-Z</button>
        <button id="exportData" type="button">Export Data</button>
        <input type="file" id="importData" accept=".json" style="display: none;">
        <button id="importButton" type="button">Import Data</button>
      </div>
    </header>

    <main>
      <section>
        <h2>Tambah Buku Baru</h2>
        <form id="bookForm" data-testid="bookForm">
          <div>
            <label for="bookFormTitle">Judul</label>
            <input id="bookFormTitle" type="text" required data-testid="bookFormTitleInput" />
          </div>
          <div>
            <label for="bookFormAuthor">Penulis</label>
            <input id="bookFormAuthor" type="text" required data-testid="bookFormAuthorInput" />
          </div>
          <div>
            <label for="bookFormYear">Tahun</label>
            <input id="bookFormYear" type="number" required data-testid="bookFormYearInput" />
          </div>
          <div>
            <label for="bookFormIsComplete">Selesai dibaca</label>
            <input
              id="bookFormIsComplete"
              type="checkbox"
              data-testid="bookFormIsCompleteCheckbox"
            />
          </div>
          <button id="bookFormSubmit" type="submit" data-testid="bookFormSubmitButton">
            Masukkan Buku ke rak <span>Belum selesai dibaca</span>
          </button>
        </form>
      </section>

      <section>
        <h2>Cari Buku</h2>
        <form id="searchBook" data-testid="searchBookForm">
          <div>
            <label for="searchBookTitle">Judul</label>
            <input id="searchBookTitle" type="text" data-testid="searchBookFormTitleInput" placeholder="Masukkan judul buku..." />
          </div>
          <div>
            <button id="searchSubmit" type="submit" data-testid="searchBookFormSubmitButton">
              Cari
            </button>
            <button id="clearSearch" type="button">
              Bersihkan
            </button>
          </div>
        </form>
      </section>

      <section>
        <h2>Belum selesai dibaca</h2>

        <div id="incompleteBookList" data-testid="incompleteBookList">
          <!-- Buku yang belum selesai dibaca akan ditampilkan di sini -->
        </div>
      </section>

      <section>
        <h2>Selesai dibaca</h2>

        <div id="completeBookList" data-testid="completeBookList">
          <!-- Buku yang sudah selesai dibaca akan ditampilkan di sini -->
        </div>
      </section>
    </main>
  </body>
</html>
