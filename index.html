<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bookshelf App</title>

    <!-- Impor script kamu di sini -->
    <!-- <PERSON><PERSON><PERSON><PERSON> `defer` jika pemanggilan dilakukan dalam <head> -->
    <script defer src="main.js"></script>
  </head>
  <body>
    <header>
      <h1>Bookshelf App</h1>
    </header>

    <main>
      <section>
        <h2>Tambah Buku Baru</h2>
        <form id="bookForm" data-testid="bookForm">
          <div>
            <label for="bookFormTitle">Judul</label>
            <input id="bookFormTitle" type="text" required data-testid="bookFormTitleInput" />
          </div>
          <div>
            <label for="bookFormAuthor">Penulis</label>
            <input id="bookFormAuthor" type="text" required data-testid="bookFormAuthorInput" />
          </div>
          <div>
            <label for="bookFormYear">Tahun</label>
            <input id="bookFormYear" type="number" required data-testid="bookFormYearInput" />
          </div>
          <div>
            <label for="bookFormIsComplete">Selesai dibaca</label>
            <input
              id="bookFormIsComplete"
              type="checkbox"
              data-testid="bookFormIsCompleteCheckbox"
            />
          </div>
          <button id="bookFormSubmit" type="submit" data-testid="bookFormSubmitButton">
            Masukkan Buku ke rak <span>Belum selesai dibaca</span>
          </button>
        </form>
      </section>

      <section>
        <h2>Cari Buku</h2>
        <form id="searchBook" data-testid="searchBookForm">
          <label for="searchBookTitle">Judul</label>
          <input id="searchBookTitle" type="text" data-testid="searchBookFormTitleInput" />
          <button id="searchSubmit" type="submit" data-testid="searchBookFormSubmitButton">
            Cari
          </button>
        </form>
      </section>

      <section>
        <h2>Belum selesai dibaca</h2>

        <div id="incompleteBookList" data-testid="incompleteBookList">
          <!-- 
            Ini adalah struktur HTML untuk masing-masing buku.
            Pastikan susunan elemen beserta atribut data-testid menyesuaikan seperti contoh ini.
          -->
          <div data-bookid="123123123" data-testid="bookItem">
            <h3 data-testid="bookItemTitle">Judul Buku 1</h3>
            <p data-testid="bookItemAuthor">Penulis: Penulis Buku 1</p>
            <p data-testid="bookItemYear">Tahun: 2030</p>
            <div>
              <button data-testid="bookItemIsCompleteButton">Selesai dibaca</button>
              <button data-testid="bookItemDeleteButton">Hapus Buku</button>
              <button data-testid="bookItemEditButton">Edit Buku</button>
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2>Selesai dibaca</h2>

        <div id="completeBookList" data-testid="completeBookList">
          <!-- 
            Ini adalah struktur HTML untuk masing-masing buku.
            Pastikan susunan elemen beserta atribut data-testid menyesuaikan seperti contoh ini.
          -->
          <div data-bookid="456456456" data-testid="bookItem">
            <h3 data-testid="bookItemTitle">Judul Buku 2</h3>
            <p data-testid="bookItemAuthor">Penulis: Penulis Buku 2</p>
            <p data-testid="bookItemYear">Tahun: 2030</p>
            <div>
              <button data-testid="bookItemIsCompleteButton">Selesai dibaca</button>
              <button data-testid="bookItemDeleteButton">Hapus Buku</button>
              <button data-testid="bookItemEditButton">Edit Buku</button>
            </div>
          </div>
        </div>
      </section>
    </main>
  </body>
</html>
